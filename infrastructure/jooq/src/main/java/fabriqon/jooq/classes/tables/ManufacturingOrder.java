/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Indexes;
import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ManufacturingOrderRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Index;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row22;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingOrder extends TableImpl<ManufacturingOrderRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.manufacturing_order</code>
     */
    public static final ManufacturingOrder MANUFACTURING_ORDER = new ManufacturingOrder();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ManufacturingOrderRecord> getRecordType() {
        return ManufacturingOrderRecord.class;
    }

    /**
     * The column <code>public.manufacturing_order.id</code>.
     */
    public final TableField<ManufacturingOrderRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.manufacturing_order.create_time</code>.
     */
    public final TableField<ManufacturingOrderRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.manufacturing_order.update_time</code>.
     */
    public final TableField<ManufacturingOrderRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.manufacturing_order.deleted</code>.
     */
    public final TableField<ManufacturingOrderRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.manufacturing_order.owner_id</code>.
     */
    public final TableField<ManufacturingOrderRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.manufacturing_order.sales_order_id</code>.
     */
    public final TableField<ManufacturingOrderRecord, UUID> SALES_ORDER_ID = createField(DSL.name("sales_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.manufacturing_order.number</code>.
     */
    public final TableField<ManufacturingOrderRecord, String> NUMBER = createField(DSL.name("number"), SQLDataType.VARCHAR(20).nullable(false), this, "");

    /**
     * The column <code>public.manufacturing_order.production_deadline</code>.
     */
    public final TableField<ManufacturingOrderRecord, LocalDateTime> PRODUCTION_DEADLINE = createField(DSL.name("production_deadline"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.manufacturing_order.status</code>.
     */
    public final TableField<ManufacturingOrderRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(32).nullable(false), this, "");

    /**
     * The column <code>public.manufacturing_order.product_id</code>.
     */
    public final TableField<ManufacturingOrderRecord, UUID> PRODUCT_ID = createField(DSL.name("product_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.manufacturing_order.quantity</code>.
     */
    public final TableField<ManufacturingOrderRecord, BigDecimal> QUANTITY = createField(DSL.name("quantity"), SQLDataType.NUMERIC(12, 4), this, "");

    /**
     * The column <code>public.manufacturing_order.manufactured_products</code>.
     */
    public final TableField<ManufacturingOrderRecord, JSONB> MANUFACTURED_PRODUCTS = createField(DSL.name("manufactured_products"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.manufacturing_order.ranking</code>.
     */
    public final TableField<ManufacturingOrderRecord, Integer> RANKING = createField(DSL.name("ranking"), SQLDataType.INTEGER, this, "");

    /**
     * The column <code>public.manufacturing_order.notes</code>.
     */
    public final TableField<ManufacturingOrderRecord, String> NOTES = createField(DSL.name("notes"), SQLDataType.VARCHAR(10000), this, "");

    /**
     * The column
     * <code>public.manufacturing_order.manufacturing_operations</code>.
     */
    public final TableField<ManufacturingOrderRecord, JSONB> MANUFACTURING_OPERATIONS = createField(DSL.name("manufacturing_operations"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.manufacturing_order.custom_product</code>.
     */
    public final TableField<ManufacturingOrderRecord, Boolean> CUSTOM_PRODUCT = createField(DSL.name("custom_product"), SQLDataType.BOOLEAN.defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public final TableField<ManufacturingOrderRecord, Object> TEXT_SEARCH = createField(DSL.name("text_search"), org.jooq.impl.DefaultDataType.getDefaultDataType("\"pg_catalog\".\"tsvector\""), this, "");

    /**
     * The column <code>public.manufacturing_order.service_id</code>.
     */
    public final TableField<ManufacturingOrderRecord, UUID> SERVICE_ID = createField(DSL.name("service_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.manufacturing_order.manufacturing_costs</code>.
     */
    public final TableField<ManufacturingOrderRecord, JSONB> MANUFACTURING_COSTS = createField(DSL.name("manufacturing_costs"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.manufacturing_order.assigned_user_id</code>.
     */
    public final TableField<ManufacturingOrderRecord, UUID> ASSIGNED_USER_ID = createField(DSL.name("assigned_user_id"), SQLDataType.UUID, this, "");

    /**
     * The column
     * <code>public.manufacturing_order.base64_client_signature</code>.
     */
    public final TableField<ManufacturingOrderRecord, String> BASE64_CLIENT_SIGNATURE = createField(DSL.name("base64_client_signature"), SQLDataType.CLOB, this, "");

    /**
     * The column <code>public.manufacturing_order.parent_id</code>.
     */
    public final TableField<ManufacturingOrderRecord, UUID> PARENT_ID = createField(DSL.name("parent_id"), SQLDataType.UUID, this, "");

    private ManufacturingOrder(Name alias, Table<ManufacturingOrderRecord> aliased) {
        this(alias, aliased, null);
    }

    private ManufacturingOrder(Name alias, Table<ManufacturingOrderRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.manufacturing_order</code> table reference
     */
    public ManufacturingOrder(String alias) {
        this(DSL.name(alias), MANUFACTURING_ORDER);
    }

    /**
     * Create an aliased <code>public.manufacturing_order</code> table reference
     */
    public ManufacturingOrder(Name alias) {
        this(alias, MANUFACTURING_ORDER);
    }

    /**
     * Create a <code>public.manufacturing_order</code> table reference
     */
    public ManufacturingOrder() {
        this(DSL.name("manufacturing_order"), null);
    }

    public <O extends Record> ManufacturingOrder(Table<O> child, ForeignKey<O, ManufacturingOrderRecord> key) {
        super(child, key, MANUFACTURING_ORDER);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.MANUFACTURING_ORDER_TEXT_SEARCH_IDX, Indexes.UNIQUE_SO_MO_ITEM_PRODUCT, Indexes.UNIQUE_SO_MO_ITEM_SERVICE);
    }

    @Override
    public UniqueKey<ManufacturingOrderRecord> getPrimaryKey() {
        return Keys.MANUFACTURING_ORDER_PKEY;
    }

    @Override
    public List<ForeignKey<ManufacturingOrderRecord, ?>> getReferences() {
        return Arrays.asList(Keys.MANUFACTURING_ORDER__MANUFACTURING_ORDER_OWNER_ID_FKEY, Keys.MANUFACTURING_ORDER__MANUFACTURING_ORDER_SALES_ORDER_ID_FKEY, Keys.MANUFACTURING_ORDER__MANUFACTURING_ORDER_SERVICE_ID_FKEY, Keys.MANUFACTURING_ORDER__MANUFACTURING_ORDER_ASSIGNED_USER_ID_FKEY, Keys.MANUFACTURING_ORDER__MANUFACTURING_ORDER_PARENT_ID_FKEY);
    }

    private transient Account _account;
    private transient SalesOrder _salesOrder;
    private transient ServiceTemplate _serviceTemplate;
    private transient Users _users;
    private transient ManufacturingOrder _manufacturingOrder;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.MANUFACTURING_ORDER__MANUFACTURING_ORDER_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.sales_order</code> table.
     */
    public SalesOrder salesOrder() {
        if (_salesOrder == null)
            _salesOrder = new SalesOrder(this, Keys.MANUFACTURING_ORDER__MANUFACTURING_ORDER_SALES_ORDER_ID_FKEY);

        return _salesOrder;
    }

    /**
     * Get the implicit join path to the <code>public.service_template</code>
     * table.
     */
    public ServiceTemplate serviceTemplate() {
        if (_serviceTemplate == null)
            _serviceTemplate = new ServiceTemplate(this, Keys.MANUFACTURING_ORDER__MANUFACTURING_ORDER_SERVICE_ID_FKEY);

        return _serviceTemplate;
    }

    /**
     * Get the implicit join path to the <code>public.users</code> table.
     */
    public Users users() {
        if (_users == null)
            _users = new Users(this, Keys.MANUFACTURING_ORDER__MANUFACTURING_ORDER_ASSIGNED_USER_ID_FKEY);

        return _users;
    }

    /**
     * Get the implicit join path to the <code>public.manufacturing_order</code>
     * table.
     */
    public ManufacturingOrder manufacturingOrder() {
        if (_manufacturingOrder == null)
            _manufacturingOrder = new ManufacturingOrder(this, Keys.MANUFACTURING_ORDER__MANUFACTURING_ORDER_PARENT_ID_FKEY);

        return _manufacturingOrder;
    }

    @Override
    public ManufacturingOrder as(String alias) {
        return new ManufacturingOrder(DSL.name(alias), this);
    }

    @Override
    public ManufacturingOrder as(Name alias) {
        return new ManufacturingOrder(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingOrder rename(String name) {
        return new ManufacturingOrder(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingOrder rename(Name name) {
        return new ManufacturingOrder(name, null);
    }

    // -------------------------------------------------------------------------
    // Row22 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row22<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, LocalDateTime, String, UUID, BigDecimal, JSONB, Integer, String, JSONB, Boolean, Object, UUID, JSONB, UUID, String, UUID> fieldsRow() {
        return (Row22) super.fieldsRow();
    }
}
