package fabriqon.e2e.dsl;

import com.jayway.jsonpath.JsonPath;
import fabriqon.app.http.controllers.manufacturing.ManufacturingOperationTemplatesController;
import fabriqon.misc.Json;
import lombok.SneakyThrows;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.http.MediaType;

import java.util.List;
import java.util.UUID;

import static fabriqon.e2e.data.Utils.smallRandomInt;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class ManufacturingOperationTemplate {

    private final Account account;
    private final Data data = new Data();
    public String operationTemplateId;

    public ManufacturingOperationTemplate(Account account) {
        this.account = account;
    }

    public ManufacturingOperationTemplate withName(String name) {
        this.data.name = name;
        return this;
    }

    public ManufacturingOperationTemplate withWorkstations(List<UUID> workstations) {
        this.data.workstationIds = workstations;
        return this;
    }

    public ManufacturingOperationTemplate withEmployees(List<UUID> employees) {
        this.data.employees = employees.stream()
                .map(e -> new ManufacturingOperationTemplatesController.EmployeeManufacturingOperation(e, false))
                .toList();
        return this;
    }

    public ManufacturingOperationTemplate withDuration(int duration) {
        this.data.duration = duration;
        return this;
    }

    @SneakyThrows
    public ManufacturingOperationTemplate create() {
        var result = account.dsl().mvc()
                .perform(post("/manufacturing/operations/templates/create")
                        .content(Json.write(new ManufacturingOperationTemplatesController.OperationDefinition(
                                data.name, data.workstationIds, data.employees, data.duration, false, null
                        )))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("id", notNullValue(String.class)))
                .andReturn();
        operationTemplateId = JsonPath.read(result.getResponse().getContentAsString(), "$.id");
        return this;
    }

    public Account account() {
        return account;
    }

    private static class Data {
        String name = "operation" + smallRandomInt();
        List<UUID> workstationIds = List.of();
        List<ManufacturingOperationTemplatesController.EmployeeManufacturingOperation> employees = List.of();
        int duration = RandomUtils.nextInt(30, 100);
    }
}
