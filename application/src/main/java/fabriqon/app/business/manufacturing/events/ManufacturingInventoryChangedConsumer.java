package fabriqon.app.business.manufacturing.events;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.business.inventory.InventoryChangedEvent;
import fabriqon.app.business.inventory.InventoryEntry;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.manufacturing.ManufacturingOrder;
import fabriqon.app.business.manufacturing.ManufacturingService;
import fabriqon.events.EventConsumer;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

import static fabriqon.jooq.classes.Tables.*;
import static fabriqon.misc.MathUtils.min;
import static org.jooq.impl.DSL.sum;

@Component
public class ManufacturingInventoryChangedConsumer extends EventConsumer<InventoryChangedEvent> {

    private final DSLContext db;
    private final InventoryService inventory;
    private final ManufacturingService manufacturingService;

    @Autowired
    public ManufacturingInventoryChangedConsumer(DSLContext db, InventoryService inventory, ManufacturingService manufacturingService) {
        this.db = db;
        this.inventory = inventory;
        this.manufacturingService = manufacturingService;
    }

    @Override
    public void process(InventoryChangedEvent event) {
        var inventoryEntry = inventoryEntry(event.data.ownerId(), event.data.inventoryEntry());
        //check if this affects the manufacturing orders
        if (inventoryEntry.salesOrderId() != null) {
            return;//no processing required for these types of entries
        }
        if (inventoryEntry.quantity().compareTo(BigDecimal.ZERO) > 0) {
            distributeStockForOrders(inventoryEntry);
        } else {
            var availableStock = inventory.getCurrentStock(inventoryEntry.ownerId(), inventoryEntry.materialGoodId(), inventoryEntry.unitId());
            var reservedStock = inventory.getReservedStock(inventoryEntry.ownerId(), inventoryEntry.materialGoodId(), inventoryEntry.unitId());
            var unassigned = availableStock.subtract(reservedStock);
            if (unassigned.compareTo(BigDecimal.ZERO) < 0) {
                removeReservedStockForOrders(inventoryEntry, unassigned);
            }
        }
    }

    private void distributeStockForOrders(InventoryEntry inventoryEntry) {
        AtomicReference<BigDecimal> available = new AtomicReference<>(inventoryEntry.quantity());
        //get orders that need the product in ranking order
        //get the orders that need the material in ranking order
        Queue<ManufacturingOrder> orders = new LinkedList<>(db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.DELETED.isFalse()
                        .and(MANUFACTURING_ORDER.OWNER_ID.eq(inventoryEntry.ownerId()))
                        .and(MANUFACTURING_ORDER.STATUS.in(ManufacturingOrder.Status.SUBMITTED.name(), ManufacturingOrder.Status.MANUFACTURING.name()))
                )
                .orderBy(MANUFACTURING_ORDER.RANKING.asc())
                .fetchInto(ManufacturingOrder.class)
                .stream()
                .filter(o -> manufacturingService.requiredMaterials(o).stream().map(m -> m.materialIds().get(0)).anyMatch(material -> material.equals(inventoryEntry.materialGoodId())))
                .toList());

        while (available.get().compareTo(BigDecimal.ZERO) > 0 && !orders.isEmpty()) {
            var order = orders.remove();
            var reservedQuantity = reservedQuantity(order, inventoryEntry.materialGoodId());
            var requiredQuantity = requiredQuantity(inventoryEntry, order);
            if (requiredQuantity.compareTo(reservedQuantity) > 0) {
                //reserve what is available
                var newlyReserved = min(available.get(), requiredQuantity.subtract(reservedQuantity));
                inventory.reserveStockForManufacturingOrder(order.ownerId(), order.id(), List.of(new InventoryService.Stock(inventoryEntry.materialGoodId(), newlyReserved)),
                        false, null);
                available.set(available.get().subtract(newlyReserved));
            }
        }
    }

    private void removeReservedStockForOrders(InventoryEntry inventoryEntry, BigDecimal toRemove) {
        //get orders that need the product in ranking order
        Queue<ManufacturingOrder> orders = new LinkedList<>(db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.DELETED.isFalse()
                        .and(MANUFACTURING_ORDER.OWNER_ID.eq(inventoryEntry.ownerId()))
                        .and(MANUFACTURING_ORDER.STATUS.in(ManufacturingOrder.Status.SUBMITTED.name(), ManufacturingOrder.Status.MANUFACTURING.name()))
                )
                .orderBy(MANUFACTURING_ORDER.RANKING.desc())
                .fetchInto(ManufacturingOrder.class)
                .stream()
                .filter(o -> manufacturingService.requiredMaterials(o).stream().map(m -> m.materialIds().get(0)).anyMatch(material -> material.equals(inventoryEntry.materialGoodId())))
                .toList());

        while (toRemove.compareTo(BigDecimal.ZERO) < 0 && !orders.isEmpty()) {
            var order = orders.remove();
            var reservedQuantity = reservedQuantity(order, inventoryEntry.materialGoodId());
            if (reservedQuantity.compareTo(BigDecimal.ZERO) > 0) {
                var toRemoveReserved = min(toRemove.abs(), reservedQuantity);
                var removed = inventory.clearReserved(null, order.id(), order.ownerId(),
                        new InventoryService.Stock(inventoryEntry.materialGoodId(), toRemoveReserved));
                toRemove = toRemove.add(removed);
            }
        }
    }

    private BigDecimal reservedQuantity(ManufacturingOrder order, UUID materialGoodId) {
        return db.select(sum(RESERVED_INVENTORY.QUANTITY))
                .from(RESERVED_INVENTORY)
                .where(RESERVED_INVENTORY.OWNER_ID.eq(order.ownerId()),
                        RESERVED_INVENTORY.MANUFACTURING_ORDER_ID.eq(order.id()),
                        RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(materialGoodId)
                )
                .fetchOptionalInto(BigDecimal.class).orElse(BigDecimal.ZERO);
    }

    private BigDecimal requiredQuantity(InventoryEntry inventoryEntry, ManufacturingOrder order) {
        return manufacturingService.requiredMaterials(order)
                .stream()
                .filter(m -> inventoryEntry.materialGoodId().equals(m.materialIds().getFirst()))
                .map(RequiredMaterial::quantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .multiply(order.quantity());
    }

    private InventoryEntry inventoryEntry(UUID ownerId, UUID entryId) {
        return db.selectFrom(INVENTORY).where(INVENTORY.OWNER_ID.eq(ownerId), INVENTORY.ID.eq(entryId)).fetchSingleInto(InventoryEntry.class);
    }

    private Account.Settings.General.InventoryAccountingSettings inventorySettings(UUID ownerId) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(ownerId))
                .fetchSingleInto(Account.class)
                .settings().general().inventoryAccountingSettings();
    }
}
